# 本地问题管理系统

一个用于本地管理项目问题的结构化模板系统。该系统提供了一个清晰的、基于文件的方法来跟踪用户故事、功能特性、缺陷和测试。

[English](README.md) | 简体中文

## 目录
- [特性](#特性)
- [目录结构](#目录结构)
- [问题类型](#问题类型)
- [问题状态](#问题状态)
- [文件命名规范](#文件命名规范)
- [模板](#模板)
- [入门指南](#入门指南)
- [使用方法](#使用方法)
- [示例](#示例)
- [最佳实践](#最佳实践)
- [编辑器集成](#编辑器集成)
- [常见问题](#常见问题)
- [贡献](#贡献)
- [许可证](#许可证)

## 特性

- 📁 按里程碑组织的结构化目录
- 📝 标准化的故事和问题模板
- ✅ 清晰的任务状态跟踪
- 🔗 内置的依赖关系管理
- 📊 通过状态历史进行进度跟踪

## 目录结构

```
.issues/
├── templates/
│   ├── story.md
│   └── issue.md
├── m001-project-milestone/
│   ├── 001-story-feature-description.md
│   ├── 002-feat-implementation.md
│   ├── 003-test-test-suite.md
│   └── 004-fix-bug-fix.md
└── m002-another-milestone/
    └── ...
```

## 问题类型

- `story`: 描述完整用户价值的用户故事
- `feat`: 新功能实现
- `fix`: 缺陷修复实现
- `test`: 测试实现

## 问题状态

- `[ ]` 未开始
- `[x]` 已完成
- `[-]` 进行中
- `[*]` 已跳过
- `[!]` 已放弃

## 文件命名规范

文件遵循以下模式：`{id}-{type}-{description}.md`

- `id`: 在所有问题中全局唯一的标识符
- `type`: 问题类型 (story/feat/fix/test)
- `description`: 简短的 kebab-case 描述

## 模板

提供两种标准模板：

1. 故事模板 (`templates/story.md`)
   ```markdown
   # 标题
   简短的故事标题

   # 故事
   作为 [角色]
   我想要 [目标/行动]
   以便 [收益/价值]

   # 验收标准
   - [ ] 标准 1
   - [ ] 标准 2

   # 子问题
   - [ ] {id} 问题描述

   # 依赖关系
   - [ ] {id} 依赖描述

   # 状态历史
   - YYYY-MM-DD: 创建
   ```

2. 问题模板 (`templates/issue.md`)
   ```markdown
   # 标题
   简短的问题标题

   # 简介
   详细的问题描述

   # 任务
   - [ ] 任务 1
   - [ ] 任务 2

   # 依赖关系
   - [ ] {id} 依赖描述

   # 状态历史
   - YYYY-MM-DD: 创建
   ```

## 入门指南

1. 克隆此仓库
2. 在你的项目中创建 `.issues` 目录
3. 将 `local-issues.mdc` 复制到你的项目 cursor rules 目录
4. 从 `issues-template/templates` 复制模板到 `.issues/templates`
5. 查看 `issues-template/sample` 中的示例以理解结构
6. 开始按照命名规范和使用模板创建你的问题

## 使用方法

### 创建新的里程碑

1. 在 `.issues/` 下创建新目录，使用模式 `m{number}-{description}`
   ```bash
   mkdir .issues/m001-user-authentication
   ```

### 创建问题

1. **用户故事**
   - 复制故事模板
   - 分配唯一ID（如 001）
   - 填写用户故事部分
   - 列出验收标准
   - 添加将要实现此故事的子问题
   ```bash
   cp .issues/templates/story.md .issues/m001-user-authentication/001-story-login-system.md
   ```

2. **功能/修复/测试问题**
   - 复制问题模板
   - 分配下一个可用ID
   - 选择适当的类型（feat/fix/test）
   - 列出具体任务
   - 链接到其他问题的依赖关系
   ```bash
   cp issues/templates/issue.md issues/m001-user-authentication/002-feat-login-page.md
   ```

### 跟踪进度

1. 使用定义的标记更新任务状态：
   ```markdown
   - [x] 已完成的任务
   - [-] 进行中的任务
   - [ ] 未开始的任务
   - [*] 已跳过的任务
   - [!] 已放弃的任务
   ```

2. 更改状态时始终更新状态历史：
   ```markdown
   # 状态历史
   - 2024-01-08: 创建
   - 2024-01-09: 开始实现
   - 2024-01-10: 完成基本功能
   ```

### 管理依赖关系

1. 使用ID引用其他问题：
   ```markdown
   # 依赖关系
   - [ ] 001 父用户故事
   - [ ] 002 后端API实现
   ```

2. 在将问题标记为完成前检查依赖关系

### 最佳工作流程实践

1. 首先创建里程碑目录
2. 从用户故事开始
3. 分解为实现问题
4. 定期更新状态
5. 保持依赖关系最新
6. 每日审查和更新任务状态

## 示例

查看 `issues-template/sample/m001-auth-system/` 中的示例实现，包括：
- 用户认证故事
- 登录页面实现
- 测试套件
- 缺陷修复任务

## 最佳实践

1. 始终使用提供的模板以保持一致性
2. 在所有问题中维护唯一ID
3. 保持状态历史更新
4. 清晰地记录依赖关系
5. 在文件名中使用有意义的描述
6. 在适当的里程碑下组织相关问题

## 编辑器集成

### Cursor
- `local-issues.mdc` 文件在你的项目 cursor rules 目录中使 Cursor 能够理解问题管理结构
- 使用 Cursor 的 markdown 预览查看问题
- 利用 Cursor 的文件导航快速切换问题
- 使用 Cursor 的搜索功能查找相关问题
- Cursor 将帮助执行 `local-issues.mdc` 中定义的命名规范和结构

### VSCode
- 安装 Markdown 预览扩展
- 使用工作区文件夹组织里程碑
- 使用集成终端创建新问题
- 启用自动换行以提高可读性

## 项目进度跟踪

你可以通过以下几种方式跟踪项目进度：
1. **里程碑概览**：检查每个里程碑内问题的完成状态
2. **状态统计**：统计里程碑内各状态的问题数量
3. **依赖关系**：审查依赖关系图以识别瓶颈
4. **历史时间线**：查看问题的状态历史以了解进度

## 常见问题

### Q: 如何处理紧急修复？
A: 在当前里程碑中创建一个新的修复问题，在简介部分注明高优先级。链接到相关的故事或功能。

### Q: 如果需要将一个问题拆分为多个问题怎么办？
A: 创建新的问题，并在原始问题的子问题部分引用它们。更新原始任务以反映拆分。

### Q: 是否应该删除已完成的问题？
A: 不，保留所有问题以便历史追踪。使用状态标记表示完成。

## 贡献

欢迎贡献！请随时提交 Pull Request。

## 许可证

本项目采用 Apache License 2.0 许可证 - 查看 LICENSE 文件了解详情。

Apache License 2.0 是一个宽松的许可证，允许你：
- 将软件用于任何目的
- 分发和修改软件
- 明确授予专利权
- 以任何许可证分发修改版本

该许可证还为用户提供了贡献者明确的专利权授权。 