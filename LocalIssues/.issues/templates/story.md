# Title
[Verb] + [Object] + [Qualifier]
Example: Create User Payment Flow

# Story
As a [user role: specific target user]
I want [goal/action: specific operation to complete]
So that [benefit/value: value gained from this operation]


# Basic Concept
- Domain Term A: [definition of the term in current business context]
- Domain Term B: [definition of the term in current business context]
- Business Rules: [key business rules to follow]
- Related Concepts: [other business concepts that need explanation]

# Scenario
Preconditions:
- [conditions that must be met before using this feature]

Main Flow:
1. [User action step 1]
2. [System response 1]
3. [User action step 2]
4. [System response 2]

Exception Handling:
- Case 1: [exception scenario description]
  - Handling: [how to handle this exception]
- Case 2: [exception scenario description]
  - Handling: [how to handle this exception]

# Acceptance Criteria
Functional Requirements:
- [ ] [specific functional acceptance criterion 1]
- [ ] [specific functional acceptance criterion 2]

Non-functional Requirements:
- [ ] [performance/security/UX acceptance criteria]


# Sub-Issues
- [ ] {id} Issue description
- [ ] {id} Issue description

# Dependencies
Technical Dependencies:
- [ ] {DEP-001} [dependent system/service/component]
- [ ] {DEP-002} [dependent API/data]

Business Dependencies:
- [ ] {BIZ-001} [dependent business process/rule]

# Status History
- YYYY-MM-DD: Created 