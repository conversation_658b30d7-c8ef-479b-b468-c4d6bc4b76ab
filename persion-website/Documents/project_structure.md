persion-website/
├── src/
│   ├── app/                 # Next.js App Router 页面
│   │   ├── about/          # 关于页面
│   │   ├── blog/           # 博客页面
│   │   ├── projects/       # 项目展示页面
│   │   ├── layout.tsx      # 根布局
│   │   └── page.tsx        # 首页
│   ├── components/         # React 组件
│   │   ├── layout/         # 布局组件 (Header, Footer)
│   │   ├── ui/             # UI 基础组件
│   │   └── project-card.tsx # 项目卡片组件
│   └── lib/                # 工具函数
├── content/                # 内容文件
│   ├── blog/              # 博客文章 (MDX)
│   └── projects/          # 项目介绍 (MDX)
├── public/                # 静态资源
└── tests-e2e/            # 端到端测试