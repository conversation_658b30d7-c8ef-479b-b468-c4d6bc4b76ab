# 实施计划：专业级个人网站构建

本文档旨在为构建一个专业级的技术作品展示与思想分享平台提供详细的实施路线图。计划基于 `.issues/m001-persional-website` 目录下的需求文档制定。

## 总体技术选型建议
- **框架:** Next.js (或 Astro) - 提供优秀的 SSG/SSR 能力、基于文件的路由和图片优化，完美契合项目需求。
- **样式:** Tailwind CSS - 用于快速实现设计系统和响应式布局。
- **内容:** MDX - 结合 Markdown 的简洁性和 React 组件的强大功能。
- **测试:** Vitest (单元/组件), Playwright (E2E)。
- **部署:** Vercel (或 Netlify)。

---

## Phase 1: 基础建设 (核心布局与设计系统)

此阶段的目标是搭建项目骨架，定义网站的"设计语言"，为后续所有页面的开发奠定基础。

```mermaid
flowchart TD
    A[确定技术栈] --> B(初始化项目);
    B --> C{定义设计规范};
    C --> D[色彩系统<br>(CSS 变量)];
    C --> E[字体系统<br>(主字体/代码字体)];
    C --> F[间距系统];
    D & E & F --> G[编写全局 CSS<br>(含 Reset/Normalize)];
    G --> H{实现核心布局与组件};
    H --> I[创建 Header 组件<br>(含导航)];
    H --> J[创建 Footer 组件];
    H --> K[创建基础组件<br>(Button, Tag)];
    I & J & K --> Z(基础建设完成);

    subgraph "设计令牌"
        D
        E
        F
    end

    style C fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#f9f,stroke:#333,stroke-width:2px
```

### 任务列表
- [ ] `P1.1` 初始化 Next.js + Tailwind CSS 项目。
- [ ] `P1.2` 在 `tailwind.config.js` 或全局 CSS 中定义设计系统的颜色、字体和间距。
- [ ] `P1.3` 创建全局 CSS 文件，导入 Tailwind 基础样式和自定义的全局样式。
- [ ] `P1.4` 创建 `Layout` 组件，包含 `<Header />` 和 `<Footer />`，作为所有页面的通用布局。
- [ ] `P1.5` 实现 `<Header />` 组件，包含网站 Logo 和导航链接（首页, 项目, 博客, 关于我）。
- [ ] `P1.6` 实现 `<Footer />` 组件，包含版权信息和社交媒体链接。
- [ ] `P1.7` 创建 `Button` 和 `Tag` 组件的初版，并应用设计系统中的样式。

---

## Phase 2: 内容引擎 (项目 & 博客)

此阶段是网站的核心，负责处理和展示所有 Markdown/MDX 内容。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Browser as 浏览器
    participant Next as Next.js Server
    participant FS as 文件系统 (content/)
    
    User->>Browser: 请求 /projects/{slug}
    Browser->>Next: GET /projects/{slug}
    Next->>FS: 读取 /content/projects/{slug}.mdx
    FS-->>Next: 返回文件内容 (Frontmatter + MDX)
    Next->>Next: 解析 Frontmatter, 渲染 MDX 为 HTML
    Next-->>Browser: 返回渲染后的 HTML 页面
    Browser->>User: 显示项目详情页
```

### 任务列表
- [ ] **通用内容处理**
    - [ ] `P2.1` 建立 `/content` 目录结构，分子目录存放项目 (`/projects`) 和博客 (`/blog`) 的 MDX 文件。
    - [ ] `P2.2` 创建一个通用的文件读取和 Frontmatter 解析工具函数。
- [ ] **项目展示引擎**
    - [ ] `P2.3` 定义项目 MDX 的 Frontmatter 结构。
    - [ ] `P2.4` 创建至少两个示例项目 MDX 文件。
    - [ ] `P2.5` 创建 `[...slug]`.tsx 动态路由页面用于展示单个项目详情。
    - [ ] `P2.6` 创建 `/projects` 页面，读取所有项目并使用 `ProjectCard` 组件进行网格布局展示。
    - [ ] `P2.7` 设计并实现 `ProjectCard` 组件。
- [ ] **博客引擎**
    - [ ] `P2.8` 定义博客 MDX 的 Frontmatter 结构。
    - [ ] `P2.9` 创建至少两个示例博客 MDX 文件。
    - [ ] `P2.10` 创建 `[...slug]`.tsx 动态路由页面用于展示单篇博客。
    - [ ] `P2.11` 创建 `/blog` 页面，读取所有博客，按日期倒序排列。
    - [ ] `P2.12` 完善 MDX 渲染，确保代码块有良好的高亮样式。

---

## Phase 3: 核心页面 (首页 & 关于我)

此阶段利用前序阶段构建的组件和内容引擎，组装成最终用户看到的核心页面。

### 任务列表
- [ ] **首页 (`/`)**
    - [ ] `P3.1` 设计并实现 "Hero" 区域，包含标语、简介和 CTA 按钮。
    - [ ] `P3.2` 实现 "精选项目" 板块，调用内容工具函数获取 2-3 个项目数据并使用 `ProjectCard` 展示。
    - [ ] `P3.3` 实现 "最新文章" 板块，获取最新 3-5 篇文章并展示。
    - [ ] `P3.4` 确保首页完全响应式。
- [ ] **关于我 (`/about`)**
    - [ ] `P3.5` 创建 `/about` 页面。
    - [ ] `P3.6` 撰写页面内容，并使用设计系统组件进行结构化展示（技能列表、经历、联系方式等）。
    - [ ] `P3.7` 整合个人照片，确保页面风格统一。

---

## Phase 4: 优化与集成 (SEO & 社交)

此阶段的目标是提升网站的可发现性和分享体验。

```mermaid
flowchart TD
    subgraph "页面请求"
        A[请求 /blog/post-1]
    end
    
    subgraph "服务器端"
        B{读取 post-1.mdx<br>获取 Frontmatter}
        C[生成页面<br>Title, Description]
        D[生成 OpenGraph<br>og:title, og:image]
        E[生成 Canonical URL]
    end

    subgraph "最终 HTML"
        F["<head>...<title>...</title>...</head>"]
    end

    A --> B;
    B --> C & D & E;
    C & D & E --> F;

    style B fill:#bbf,stroke:#333,stroke-width:2px
```

### 任务列表
- [ ] `P4.1` 实现动态元数据生成机制。在 `Layout` 或页面级别根据 Frontmatter 数据设置 `<title>` 和 `<meta name="description">`。
- [ ] `P4.2` 实现动态 Open Graph 和 Twitter Card 标签生成。
- [ ] `P4.3` 为所有页面添加 Canonical URL 链接。
- [ ] `P4.4` 创建 `sitemap.xml` 的生成脚本，并在构建时运行。
- [ ] `P4.5` 创建 `robots.txt` 文件。
- [ ] `P4.6` 创建 RSS feed (`feed.xml`) 的生成脚本，并在构建时运行。

---

## Phase 5: 质量保证与自动化 (测试 & CI/CD)

此阶段确保项目代码质量和部署流程的自动化、可靠性。

```mermaid
sequenceDiagram
    actor Dev as 开发者
    participant Repo as Git Repository
    participant Actions as GitHub Actions
    participant Vercel as 部署平台

    Dev->>Repo: git push
    Repo->>Actions: Trigger "on push" workflow
    Actions->>Actions: Run Linting
    Actions->>Actions: Run Unit Tests (Vitest)
    
    alt on merge to main
        Dev->>Repo: Merge PR to main
        Repo->>Actions: Trigger "on merge" workflow
        Actions->>Actions: Build Project
        note right of Actions: If build fails, stop.
        Actions->>Actions: Run E2E Tests (Playwright)
        note right of Actions: If tests fail, stop.
        Actions->>Vercel: Deploy to Production
        Vercel-->>Actions: Deployment Status
    end
```

### 任务列表
- [ ] `P5.1` 安装并配置 Vitest。
- [ ] `P5.2` 为核心组件（如 `Button`, `ProjectCard`, `Layout`）编写单元测试。
- [ ] `P5.3` 安装并配置 Playwright。
- [ ] `P5.4` 编写 E2E 测试，覆盖核心用户路径（首页 -> 博客列表 -> 博客详情，首页 -> 项目列表 -> 项目详情）。
- [ ] `P5.5` 创建 GitHub Actions workflow (`.github/workflows/ci.yml`)。
- [ ] `P5.6` 在 CI 工作流中配置 Linting 和单元测试，在 `push` 事件上触发。
- [ ] `P5.7` 在 CI 工作流中配置构建、E2E 测试和部署步骤，在合并到 `main` 分支时触发。 