import { test, expect } from '@playwright/test';

test('Homepage has correct title and navigates to projects', async ({ page }) => {
  // Go to the home page and wait for it to be fully loaded.
  await page.goto('/', { waitUntil: 'networkidle' });

  // Expect a title "to contain" a substring.
  await expect(page).toHaveTitle(/My Personal Website/);

  // Find the "View My Projects" button, ensure it's visible, and then click it.
  const viewProjectsLink = page.getByRole('link', { name: 'View My Projects' });
  await expect(viewProjectsLink).toBeVisible();
  await viewProjectsLink.click();

  // Expects the URL to be /projects.
  await expect(page).toHaveURL(/.*projects/);

  // Expects the page to have a heading with the name of My Projects.
  await expect(page.getByRole('heading', { name: 'My Projects' })).toBeVisible();
});

test('Project page navigation works', async ({ page }) => {
    await page.goto('/projects', { waitUntil: 'networkidle' });
    
    // Get the title of the first project card
    const firstProjectCard = page.locator('a[href^="/projects/"]').first();
    await expect(firstProjectCard).toBeVisible();
    const projectTitle = await firstProjectCard.locator('h3').textContent();

    // Click the first project card
    await firstProjectCard.click();

    // Expects the URL to be /projects/project-one (or similar).
    await expect(page).toHaveURL(/.*projects\/.+/);

    // Expects the page to have a heading with the name of the project.
    await expect(page.getByRole('heading', { name: projectTitle! })).toBeVisible();
});
