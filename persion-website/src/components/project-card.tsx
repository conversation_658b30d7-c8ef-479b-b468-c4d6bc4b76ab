import Link from "next/link";
import Image from "next/image";
import { Badge } from "./ui/badge";

interface ProjectCardProps {
  slug: string;
  title: string;
  summary: string;
  coverImage: string;
  tags: string[];
}

export function ProjectCard({ slug, title, summary, coverImage, tags }: ProjectCardProps) {
  return (
    <Link href={`/projects/${slug}`} className="block group">
      <div className="border rounded-lg overflow-hidden transition-shadow duration-300 group-hover:shadow-xl">
        <div className="relative h-48 w-full">
          <Image
            src={coverImage}
            alt={`Cover image for ${title}`}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
        </div>
        <div className="p-4">
          <h3 className="text-lg font-bold mb-2">{title}</h3>
          <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">{summary}</p>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge key={tag} variant="secondary">{tag}</Badge>
            ))}
          </div>
        </div>
      </div>
    </Link>
  );
} 