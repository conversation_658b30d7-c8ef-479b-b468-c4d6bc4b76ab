import { render, screen, fireEvent } from '@testing-library/react';
import { expect, test, vi } from 'vitest';
import { Button } from './button';

test('But<PERSON> renders and is clickable', () => {
  const handleClick = vi.fn();
  
  render(<Button onClick={handleClick}>Click Me</Button>);
  
  const buttonElement = screen.getByText(/Click Me/i);
  
  // Check if the button is in the document
  expect(buttonElement).toBeInTheDocument();
  
  // Simulate a click
  fireEvent.click(buttonElement);
  
  // Check if the click handler was called
  expect(handleClick).toHaveBeenCalledTimes(1);
}); 