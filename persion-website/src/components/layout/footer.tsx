import Link from "next/link";

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="py-4 px-6 md:px-8 border-t">
      <div className="container mx-auto flex justify-between items-center text-sm text-neutral-500">
        <p>&copy; {currentYear} MySite. All rights reserved.</p>
        <div className="flex gap-4">
          <Link href="https://github.com" target="_blank" rel="noopener noreferrer" className="hover:text-neutral-900 dark:hover:text-neutral-100">
            GitHub
          </Link>
          <Link href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="hover:text-neutral-900 dark:hover:text-neutral-100">
            LinkedIn
          </Link>
        </div>
      </div>
    </footer>
  );
} 