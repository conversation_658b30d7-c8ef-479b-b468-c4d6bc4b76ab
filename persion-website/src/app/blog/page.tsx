import Link from "next/link";
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { format, parseISO } from 'date-fns';

function getAllPosts() {
  const postsDirectory = path.join(process.cwd(), 'content', 'blog');
  if (!fs.existsSync(postsDirectory)) return [];

  const fileNames = fs.readdirSync(postsDirectory);

  const allPosts = fileNames.map(fileName => {
    const slug = fileName.replace(/\.mdx?$/, '');
    const fullPath = path.join(postsDirectory, fileName);
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data } = matter(fileContents);
    
    if (data.date) {
      data.date = format(parseISO(data.date), 'MMMM d, yyyy');
    }

    return { slug, ...data };
  });

  return allPosts.sort((a: any, b: any) => {
    const dateA = new Date(matter(fs.readFileSync(path.join(postsDirectory, `${a.slug}.mdx`), 'utf8')).data.date);
    const dateB = new Date(matter(fs.readFileSync(path.join(postsDirectory, `${b.slug}.mdx`), 'utf8')).data.date);
    return dateB.getTime() - dateA.getTime();
  });
}

export const metadata = {
  title: "Blog",
  description: "My thoughts on technology, development, and more.",
};

export default function BlogPage() {
  const posts = getAllPosts();

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-4xl font-extrabold text-center mb-12">My Blog</h1>
      <div className="max-w-2xl mx-auto">
        <ul className="space-y-10">
          {posts.map((post: any) => (
            <li key={post.slug}>
              <article>
                <Link href={`/blog/${post.slug}`} className="block group">
                  <h2 className="text-2xl font-bold group-hover:text-primary transition-colors">{post.title}</h2>
                  <p className="text-muted-foreground text-sm mt-1 mb-2">{post.date}</p>
                  <p className="text-muted-foreground">{post.summary}</p>
                </Link>
              </article>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
