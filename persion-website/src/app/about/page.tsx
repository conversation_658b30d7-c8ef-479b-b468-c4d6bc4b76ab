import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";

export const metadata = {
  title: "About Me",
  description: "Learn more about my skills, experience, and what drives me.",
};

const skills = {
  "Frontend": ["React", "Next.js", "TypeScript", "Tailwind CSS", "Redux"],
  "Backend": ["Node.js", "Express", "Python", "Flask", "PostgreSQL"],
  "DevOps & Tools": ["Docker", "Git", "Vercel", "GitHub Actions", "Jest"],
};

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        {/* Header Section */}
        <section className="text-center mb-12">
          <Image
            src="/images/placeholder.svg" // Replace with your actual photo
            alt="My Photo"
            width={150}
            height={150}
            className="rounded-full mx-auto mb-4"
          />
          <h1 className="text-4xl font-extrabold">About Me</h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-2">
            A passionate developer on a mission to build great software.
          </p>
        </section>

        {/* Story Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">My Story</h2>
          <div className="prose dark:prose-invert max-w-none space-y-4">
            <p>
              My journey into the world of technology began with a simple "Hello, World!" and quickly blossomed into a full-fledged passion. I thrive on the challenge of solving complex problems and the joy of seeing my code come to life, making a real impact.
            </p>
            <p>
              I believe in writing clean, scalable, and maintainable code. For me, software development is not just about building features; it's about crafting experiences, architecting solutions, and continuously learning and growing in a field that's always evolving.
            </p>
          </div>
        </section>

        {/* Skills Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-4">My Skills</h2>
          <div className="space-y-6">
            {Object.entries(skills).map(([category, skillList]) => (
              <div key={category}>
                <h3 className="text-xl font-semibold mb-3">{category}</h3>
                <div className="flex flex-wrap gap-2">
                  {skillList.map((skill) => (
                    <Badge key={skill} variant="default">{skill}</Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>
        
        {/* Contact Section */}
        <section className="text-center">
            <h2 className="text-3xl font-bold mb-4">Get In Touch</h2>
            <p className="mb-4 text-neutral-700 dark:text-neutral-300">I'm always open to discussing new projects, creative ideas, or opportunities to be part of an amazing team. Feel free to reach out!</p>
            <Link href="mailto:<EMAIL>" className="text-primary text-lg hover:underline">
                <EMAIL>
            </Link>
        </section>
      </div>
    </div>
  );
}
