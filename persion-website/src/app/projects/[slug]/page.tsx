import { notFound } from "next/navigation";
import Image from 'next/image';
import { MDXRemote } from "next-mdx-remote/rsc";
import { Badge } from "@/components/ui/badge";
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

const contentDirectory = path.join(process.cwd(), 'content');

export async function generateStaticParams() {
  const projectsDirectory = path.join(contentDirectory, 'projects');
  if (!fs.existsSync(projectsDirectory)) return [];
  const filenames = fs.readdirSync(projectsDirectory);
  return filenames.map(filename => ({
    slug: filename.replace(/\.mdx$/, '')
  }));
}

async function getContentBySlug(slug: string) {
  const fullPath = path.join(contentDirectory, 'projects', `${slug}.mdx`);
  if (!fs.existsSync(fullPath)) return null;

  const fileContents = fs.readFileSync(fullPath, 'utf8');
  const { data, content } = matter(fileContents);

  return { slug, ...data, content } as {
    slug: string;
    title: string;
    summary: string;
    coverImage: string;
    tags: string[];
    content: string;
  };
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const project = await getContentBySlug(params.slug);
  if (!project) {
    return {
      title: 'Project Not Found'
    };
  }
  return { 
    title: `${project.title} | My Personal Website`,
    description: project.summary,
    openGraph: {
        title: project.title,
        description: project.summary,
        images: [project.coverImage],
    },
    twitter: {
        card: 'summary_large_image',
        title: project.title,
        description: project.summary,
        images: [project.coverImage],
    },
    alternates: {
      canonical: `/projects/${params.slug}`,
    },
  };
}

export default async function ProjectPage({ params }: { params: { slug: string } }) {
  const project = await getContentBySlug(params.slug);

  if (!project) {
    notFound();
  }

  const { title, summary, coverImage, tags, content } = project;

  return (
    <article className="container mx-auto px-4 py-12">
      <header className="mb-8 text-center">
        <h1 className="text-4xl font-extrabold mb-2">{title}</h1>
        <p className="text-lg text-muted-foreground">{summary}</p>
        <div className="mt-4 flex justify-center gap-2">
          {tags.map((tag: string) => (
            <Badge key={tag} variant="secondary">{tag}</Badge>
          ))}
        </div>
      </header>

      <div className="relative w-full h-96 rounded-lg overflow-hidden mb-8">
        <Image
          src={coverImage}
          alt={`Cover for ${title}`}
          fill
          className="object-cover"
          priority
        />
      </div>

      <div className="prose dark:prose-invert max-w-none mx-auto">
        <MDXRemote source={content} />
      </div>
    </article>
  );
} 