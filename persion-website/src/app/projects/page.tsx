import { ProjectCard } from "@/components/project-card";
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

function getAllProjects() {
  const projectsDirectory = path.join(process.cwd(), 'content', 'projects');
  if (!fs.existsSync(projectsDirectory)) return [];
  
  const fileNames = fs.readdirSync(projectsDirectory);
  
  return fileNames.map(fileName => {
    const slug = fileName.replace(/\.mdx?$/, '');
    const fullPath = path.join(projectsDirectory, fileName);
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data } = matter(fileContents);
    return { slug, ...data };
  });
}

export const metadata = {
  title: "Projects",
  description: "A showcase of my projects and work.",
};

export default function ProjectsPage() {
  const projects = getAllProjects();

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-4xl font-extrabold text-center mb-12">My Projects</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {projects.map((project: any) => (
          <ProjectCard
            key={project.slug}
            slug={project.slug}
            title={project.title}
            summary={project.summary}
            coverImage={project.coverImage}
            tags={project.tags}
          />
        ))}
      </div>
    </div>
  );
} 