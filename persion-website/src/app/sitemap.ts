import { MetadataRoute } from 'next'
import { getAllContent } from '@/lib/content'

const URL = 'https://your-domain.com' // Replace with your domain

export default function sitemap(): MetadataRoute.Sitemap {
  const posts = getAllContent('blog').map(({ slug }) => ({
    url: `${URL}/blog/${slug}`,
    lastModified: new Date().toISOString(),
  }))

  const projects = getAllContent('projects').map(({ slug }) => ({
    url: `${URL}/projects/${slug}`,
    lastModified: new Date().toISOString(),
  }))

  const routes = ['', '/about', '/blog', '/projects'].map((route) => ({
    url: `${URL}${route}`,
    lastModified: new Date().toISOString(),
  }))

  return [...routes, ...posts, ...projects]
} 