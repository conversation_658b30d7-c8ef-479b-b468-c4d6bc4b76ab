import RSS from 'rss';
import { getAllContent } from '@/lib/content';

const URL = 'https://your-domain.com'; // Replace with your domain

export async function GET() {
  const feed = new RSS({
    title: 'My Personal Website Blog',
    description: 'My thoughts on technology, development, and more.',
    site_url: URL,
    feed_url: `${URL}/feed.xml`,
    language: 'en',
  });

  const posts = getAllContent('blog');

  posts.forEach((post: any) => {
    feed.item({
      title: post.title,
      description: post.summary,
      url: `${URL}/blog/${post.slug}`,
      guid: post.slug,
      date: post.date,
    });
  });

  return new Response(feed.xml({ indent: true }), {
    headers: {
      'Content-Type': 'application/atom+xml; charset=utf-8',
    },
  });
} 