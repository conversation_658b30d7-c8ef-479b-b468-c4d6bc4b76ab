import Link from "next/link";
import { ProjectCard } from "@/components/project-card";
import { But<PERSON> } from "@/components/ui/button";
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { format, parseISO } from 'date-fns';

function getAllContent(contentType: 'blog' | 'projects') {
  const typeDirectory = path.join(process.cwd(), 'content', contentType);
  if (!fs.existsSync(typeDirectory)) return [];

  const fileNames = fs.readdirSync(typeDirectory);

  const allContent = fileNames.map(fileName => {
    const slug = fileName.replace(/\.mdx?$/, '');
    const fullPath = path.join(typeDirectory, fileName);
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data } = matter(fileContents);

    const formattedData = { ...data };

    if (contentType === 'blog' && typeof formattedData.date === 'string') {
        try {
            const parsedDate = parseISO(formattedData.date);
            formattedData.date = format(parsedDate, 'MMMM d, yyyy');
        } catch (error) {
            console.error(`Invalid date format in ${fileName}. Using original value.`);
        }
    }

    return {
      slug,
      ...formattedData,
    } as any;
  });

  if (contentType === 'blog') {
    return allContent.sort((a, b) => {
        const dateA_data = matter(fs.readFileSync(path.join(typeDirectory, `${a.slug}.mdx`), 'utf8')).data;
        const dateB_data = matter(fs.readFileSync(path.join(typeDirectory, `${b.slug}.mdx`), 'utf8')).data;
        const dateA = dateA_data.date ? new Date(parseISO(dateA_data.date)) : new Date(0);
        const dateB = dateB_data.date ? new Date(parseISO(dateB_data.date)) : new Date(0);
        return dateB.getTime() - dateA.getTime();
      })
  }

  return allContent;
}

export default function HomePage() {
  const allProjects = getAllContent("projects");
  const featuredProjects = allProjects.slice(0, 2);

  const allPosts = getAllContent("blog");
  const latestPosts = allPosts.slice(0, 3);

  return (
    <>
      {/* Hero Section */}
      <section className="text-center py-20">
        <div className="container mx-auto px-4">
          <h1 className="text-5xl font-extrabold mb-4">Welcome to My Digital Garden</h1>
          <p className="text-xl text-muted-foreground mb-8">
            I build things for the web. Explore my projects and thoughts.
          </p>
          <Button asChild size="lg">
            <Link href="/projects">View My Projects</Link>
          </Button>
        </div>
      </section>

      {/* Featured Projects Section */}
      <section id="featured-projects" className="py-20 bg-muted/40">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Featured Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {featuredProjects.map((project) => (
              <ProjectCard
                key={project.slug}
                slug={project.slug}
                title={project.title}
                summary={project.summary}
                coverImage={project.coverImage}
                tags={project.tags}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Latest Posts Section */}
      <section id="latest-posts" className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Latest Posts</h2>
          <div className="max-w-2xl mx-auto">
            <ul className="space-y-8">
              {latestPosts.map((post) => (
                <li key={post.slug}>
                  <Link href={`/blog/${post.slug}`} className="block group">
                    <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">{post.title}</h3>
                    <p className="text-muted-foreground text-sm mb-2">{post.date}</p>
                    <p className="text-muted-foreground">{post.summary}</p>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>
    </>
  );
}
