# 标题
测试体系与自动化流程建立

# 简介
为了保证网站的长期健康和高质量，以及实现快速、可靠的迭代，建立一套自动化的测试与部署流程是不可或缺的。此任务旨在为网站构建一个完整的 CI/CD（持续集成/持续部署）管道。

# 任务
- [ ] **引入单元/组件测试框架:** 集成 Vitest 或 Jest，为核心的可复用组件（例如 `ProjectCard`, `Button`）编写单元测试，确保它们的行为符合预期。
- [ ] **引入端到端 (E2E) 测试框架:** 集成 Playwright 或 Cypress，编写覆盖关键用户路径的自动化测试用例：
    - [ ] 测试从首页到项目列表页，再到具体项目详情页的导航。
    - [ ] 测试从首页到博客列表页，再到具体文章详情页的导航。
    - [ ] 测试所有主要页面的链接是否有效，确保没有死链。
- [ ] **建立 CI/CD 管道 (以 GitHub Actions 为例):**
    - [ ] 配置工作流，使得每次向代码仓库推送（push）时，自动运行代码风格检查（Linting）和所有单元测试。
    - [ ] 配置工作流，使得每次当代码合并到 `main` 分支时，触发完整的构建流程。
    - [ ] 在构建成功后，自动运行所有 E2E 测试。
    - [ ] 在所有测试通过后，自动将网站部署到你的托管平台（如 Vercel, Netlify）。

# 依赖关系
- [ ] 所有其他功能模块的完成。

# 状态历史
- 2025-07-06: Created