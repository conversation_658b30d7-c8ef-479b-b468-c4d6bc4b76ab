# 标题
核心布局与设计系统构建

# 简介
定义网站的全局视觉规范和基础布局结构。这是保证网站一致性、可维护性和专业度的基石。我们将建立一套包含颜色、字体、间距的“设计语言”，并实现可复用的核心布局组件，如页头、页脚和导航栏。

# 任务
- [ ] 确定主色板、辅助色和中性色（灰度），并定义为 CSS 自定义属性 (CSS Custom Properties)。
- [ ] 选择并配置网站的主字体和代码字体，并定义好字号、行高、字重等排版层级。
- [ ] 定义全局间距单元 (spacing unit)，用于所有组件和布局的 margin/padding。
- [ ] 创建全局 CSS 文件，包含 reset/normalize 样式和上述设计令牌 (design tokens)。
- [ ] 实现网站的响应式主布局，包括一个常驻的 Header (含导航) 和一个 Footer。
- [ ] 设计并实现基础可复用组件的初始样式：按钮 (Button)、标签 (Tag)。

# 依赖关系
- [ ] 001 构建一个专业级的技术作品展示与思想分享平台

# 状态历史
- 2025-07-06: Created