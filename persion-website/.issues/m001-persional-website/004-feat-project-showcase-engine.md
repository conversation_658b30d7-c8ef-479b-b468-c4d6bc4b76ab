# 标题
项目展示引擎构建

# 简介
这一部分是作品集的核心。我们要建立一个以 Markdown 文件为数据源的、可扩展的项目管理和展示系统。这包括定义项目的数据结构，创建一个聚合所有项目的列表页，以及为每个项目生成一个独立的、内容详尽的详情页。

# 任务
- [ ] **定义项目数据模型:** 在 Markdown 的 Frontmatter 中确定一个标准的项目数据结构。至少应包含：`title`, `coverImage` (项目封面图), `tags` (如 "后端", "数据可视化"), `techStack` (具体技术点), `summary` (一句话简介), `repoUrl` (代码仓库链接), `demoUrl` (在线演示链接)。
- [ ] 创建至少两个示例项目的 Markdown 文件，用以测试数据模型和页面渲染。
- [ ] **实现项目列表页 (`/projects`):** 创建一个页面，它能自动读取所有项目的 Markdown 文件，并将它们以“项目卡片”的形式展示在一个响应式的网格布局中。
- [ ] **完善 `ProjectCard` 组件:** 升级在首页用过的项目卡片组件，使其展示更丰富的信息（如技术栈标签），并确保其在项目列表页中完美复用。
- [ ] **实现项目详情页 (动态路由):** 设置动态路由，以便为每个项目生成一个独一无二的 URL（例如 `/projects/my-cool-project`）。
- [ ] **创建项目详情页模板:** 设计并实现一个用于展示单个项目详情的页面模板。该模板需要能优雅地渲染 Markdown 文件的主体内容（这里是你的项目故事、挑战和解决方案），并在侧边栏或顶部清晰地展示项目的核心元数据（技术栈、仓库链接、演示链接等）。

# 依赖关系
- [ ] 002 核心布局与设计系统构建

# 状态历史
- 2025-07-06: Created