# 标题
主页设计与实现

# 简介
主页是网站的第一印象。它必须清晰地阐述我是谁、我做什么，并能快速引导访客前往最有价值的区域（博客和项目）。它不仅仅是一个欢迎页面，更是一个通往核心内容的发射台。

# 任务
- [ ] 构思并撰写 "Hero"区域的核心文案：一句精炼、有力的标语（例如：“一个热衷于构建优雅解决方案的全栈开发者”），一段简短的自我介绍，以及清晰的行动号召（Call-to-Action）按钮，分别指向“查看项目”和“阅读博客”。
- [ ] 设计一个“精选项目”板块，用于突出展示 2-3 个最具代表性的项目。这只是一个预告，不是完整列表。每个精选项目都应链接到其详情页。
- [ ] 设计一个“最新文章”板块，用于展示最近发布的 3-5 篇博客文章，以体现网站的活跃度。每个条目都应链接到完整的文章页。
- [ ] 基于设计的布局，实现主页的 HTML 语义化结构。
- [ ] 使用已建立的设计系统中的变量和组件来应用样式。**禁止为主页编写一次性的、孤立的样式**；如果需要新组件，优先考虑是否应将其纳入设计系统。
- [ ] 确保主页布局在移动端、平板和桌面端都完全响应式，并拥有出色的视觉效果。

# 依赖关系
- [ ] 002 核心布局与设计系统构建

# 状态历史
- 2025-07-06: Created