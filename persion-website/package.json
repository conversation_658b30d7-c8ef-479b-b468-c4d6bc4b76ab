{"name": "temp-next-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:e2e": "playwright test"}, "dependencies": {"@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "gray-matter": "^4.0.3", "next": "14.2.3", "next-mdx-remote": "^5.0.0", "react": "^18", "react-dom": "^18", "rss": "^1.2.2", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@playwright/test": "^1.44.1", "@tailwindcss/typography": "^0.5.13", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/rss": "^0.0.32", "@vitejs/plugin-react": "^4.3.0", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.2.3", "jsdom": "^24.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^1.6.0"}}